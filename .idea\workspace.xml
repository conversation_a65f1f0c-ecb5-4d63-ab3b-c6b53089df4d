<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="497205d3-f3ef-487b-b5a2-bdf4de0b914b" name="Changes" comment="5.2 数据集基础框架（继承TableDataset）" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$/train-anything" value="dev" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/train-anything" />
    <option name="RESET_MODE" value="HARD" />
    <option name="ROOT_SYNC" value="DONT_SYNC" />
  </component>
  <component name="GitLabMergeRequestFiltersHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPENED&quot;,
    &quot;assignee&quot;: {
      &quot;type&quot;: &quot;org.jetbrains.plugins.gitlab.mergerequest.ui.filters.GitLabMergeRequestsFiltersValue.MergeRequestsMemberFilterValue.MergeRequestsAssigneeFilterValue&quot;,
      &quot;username&quot;: &quot;lanxin&quot;,
      &quot;fullname&quot;: &quot;<EMAIL>&quot;
    }
  }
}</component>
  <component name="GitLabMergeRequestsSettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;first&quot;: &quot;https://gitlab.liebaopay.com/datax/train-anything.git&quot;,
    &quot;second&quot;: &quot;79f8ca7c-dbe3-4838-b94e-982156044d75&quot;
  }
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2zi65llJiCyZE8jiZDolOl2Ngd4" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;Python.analyze_coco_dataset.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowPanel.toolwindow.highlight.mappings&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowPanel.toolwindow.highlight.symlinks&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowPanel.toolwindow.show.date&quot;: &quot;false&quot;,
    &quot;WebServerToolWindowPanel.toolwindow.show.permissions&quot;: &quot;false&quot;,
    &quot;WebServerToolWindowPanel.toolwindow.show.size&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;feat/train__tsr__lore&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/workspace/projects/TSRTransplantation/this_vibecoding/vibe_utils&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\workspace\projects\TSRTransplantation\this_vibecoding\vibe_utils" />
      <recent name="D:\workspace\projects\TSRTransplantation\train-anything\vibe_coding_lore\2-migration_lore" />
      <recent name="D:\workspace\projects\TSRTransplantation\train-anything\vibecoding_lore" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\workspace\projects\TSRTransplantation\train-anything\vibe_coding_lore\2-migration_lore\migration_reports" />
      <recent name="D:\workspace\projects\TSRTransplantation\this_vibecoding\docs\bak" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="analyze_coco_dataset" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="TSRTransplantation" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="SDK_NAME" value="Python 3.10" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/this_vibecoding/vibe_utils" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/this_vibecoding/vibe_utils/analyze_coco_dataset.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.analyze_coco_dataset" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-PY-251.23774.444" />
        <option value="bundled-python-sdk-890ed5b35930-d9c5bdb153f4-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.23774.444" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="497205d3-f3ef-487b-b5a2-bdf4de0b914b" name="Changes" comment="" />
      <created>1752201365992</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752201365992</updated>
      <workItem from="1752201367127" duration="5037000" />
      <workItem from="1752491451775" duration="5581000" />
      <workItem from="1752502252347" duration="439000" />
      <workItem from="1752502705545" duration="490000" />
      <workItem from="1752503605268" duration="259000" />
      <workItem from="1752504066504" duration="11995000" />
      <workItem from="1752556316978" duration="16394000" />
      <workItem from="1752589127912" duration="174000" />
      <workItem from="1752589760086" duration="64000" />
      <workItem from="1752589839572" duration="1506000" />
      <workItem from="1752591435670" duration="24470000" />
      <workItem from="1752721093439" duration="25416000" />
      <workItem from="1752809820235" duration="25731000" />
      <workItem from="1752945273221" duration="13360000" />
      <workItem from="1752982373430" duration="16339000" />
    </task>
    <task id="LOCAL-00001" summary="1. 构建LORE项目迁移的需求规划文档和渐进式迭代步骤一（制定蓝本、创建目录）">
      <option name="closed" value="true" />
      <created>1752507922842</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752507922842</updated>
    </task>
    <task id="LOCAL-00002" summary="1. 构建LORE项目迁移的需求规划文档和渐进式迭代步骤一（制定蓝本、创建目录）">
      <option name="closed" value="true" />
      <created>1752507970843</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1752507970843</updated>
    </task>
    <task id="LOCAL-00003" summary="1. 构建LORE项目迁移的需求规划文档完成渐进式迭代步骤一（制定蓝本、创建目录、输出报告）">
      <option name="closed" value="true" />
      <created>1752653297174</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1752653297174</updated>
    </task>
    <task id="LOCAL-00004" summary="2. 复制并隔离编译依赖项。具体是将 LORE-TSR 项目中需要手动编译的外部依赖 `DCNv2` 和 `nms.pyx` 原封不动地复制到 train-anything 框架的 `external/lore_tsr/` 目录下">
      <option name="closed" value="true" />
      <created>1752658928724</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1752658928724</updated>
    </task>
    <task id="LOCAL-00005" summary="3. 将实现核心算法的文件（不包括模型创建/加载的工厂文件）近乎逐字地复制到 `train-anything` 的 `networks/lore_tsr/` 目录中。这一步迁移了模型的计算核心，是后续适配和重构的基础。">
      <option name="closed" value="true" />
      <created>1752669555658</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1752669555658</updated>
    </task>
    <task id="LOCAL-00006" summary="4. 迁移 `LORE-TSR` 中负责创建和加载模型的工厂函数 `create_model`。这是连接配置文件和模型实体的关键“胶水代码”。">
      <option name="closed" value="true" />
      <created>1752673906391</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1752673906391</updated>
    </task>
    <task id="LOCAL-00007" summary="1.1 创建基础目录结构和模块初始化">
      <option name="closed" value="true" />
      <created>1752838402915</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1752838402915</updated>
    </task>
    <task id="LOCAL-00008" summary="1.2 创建配置文件和参数解析">
      <option name="closed" value="true" />
      <created>1752842522569</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1752842522569</updated>
    </task>
    <task id="LOCAL-00009" summary="1.3 创建训练入口空框架">
      <option name="closed" value="true" />
      <created>1752844983148</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1752844983148</updated>
    </task>
    <task id="LOCAL-00010" summary="2.1 创建模型工厂函数和基础架构适配">
      <option name="closed" value="true" />
      <created>1752970604915</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1752970604915</updated>
    </task>
    <task id="LOCAL-00011" summary="2.2 迁移主要骨干网络">
      <option name="closed" value="true" />
      <created>1752971932471</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1752971932471</updated>
    </task>
    <task id="LOCAL-00012" summary="2.3 创建轻量级检测头工具和接口层">
      <option name="closed" value="true" />
      <created>1752977768762</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1752977768762</updated>
    </task>
    <task id="LOCAL-00013" summary="2.4 完善训练入口中的模型创建和初始化逻辑">
      <option name="closed" value="true" />
      <created>1752977794973</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1752977794973</updated>
    </task>
    <task id="LOCAL-00014" summary="3.1 建立最小训练循环框架">
      <option name="closed" value="true" />
      <created>1752982894217</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1752982894217</updated>
    </task>
    <task id="LOCAL-00015" summary="3.2 完善训练循环稳定性和完整性">
      <option name="closed" value="true" />
      <created>1752984666348</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1752984666348</updated>
    </task>
    <task id="LOCAL-00016" summary="4.1 核心损失函数实现">
      <option name="closed" value="true" />
      <created>1752990706738</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1752990706738</updated>
    </task>
    <task id="LOCAL-00017" summary="4.2 配置系统扩展">
      <option name="closed" value="true" />
      <created>1752990816952</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1752990816952</updated>
    </task>
    <task id="LOCAL-00018" summary="4.3 训练循环集成">
      <option name="closed" value="true" />
      <created>1752992689602</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1752992689602</updated>
    </task>
    <task id="LOCAL-00019" summary="5.1 基础工具函数迁移（最小可验证单元）">
      <option name="closed" value="true" />
      <created>1753002602060</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1753002602060</updated>
    </task>
    <task id="LOCAL-00020" summary="5.2 数据集基础框架（继承TableDataset）">
      <option name="closed" value="true" />
      <created>1753004164317</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1753004164317</updated>
    </task>
    <option name="localTasksCounter" value="21" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="feat/train_tsr_xelawk" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="COLUMN_ID_WIDTH">
                <map>
                  <entry key="Table.Default.Author.ColumnIdWidth" value="114" />
                  <entry key="Table.Default.Date.ColumnIdWidth" value="143" />
                  <entry key="Table.GitHub.CommitStatus.ColumnIdWidth" value="15" />
                </map>
              </option>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="feat/train_tsr_lore" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="1. 构建LORE项目迁移的需求规划文档和渐进式迭代步骤一（制定蓝本、创建目录）" />
    <MESSAGE value="tmp save" />
    <MESSAGE value="drop" />
    <MESSAGE value="1. 构建LORE项目迁移的需求规划文档完成渐进式迭代步骤一（制定蓝本、创建目录、输出报告）" />
    <MESSAGE value="2. 复制并隔离编译依赖项。具体是将 LORE-TSR 项目中需要手动编译的外部依赖 `DCNv2` 和 `nms.pyx` 原封不动地复制到 train-anything 框架的 `external/lore_tsr/` 目录下" />
    <MESSAGE value="3. 将实现核心算法的文件（不包括模型创建/加载的工厂文件）近乎逐字地复制到 `train-anything` 的 `networks/lore_tsr/` 目录中。这一步迁移了模型的计算核心，是后续适配和重构的基础。" />
    <MESSAGE value="4. 迁移 `LORE-TSR` 中负责创建和加载模型的工厂函数 `create_model`。这是连接配置文件和模型实体的关键“胶水代码”。" />
    <MESSAGE value="trouble-step5" />
    <MESSAGE value="1.1 创建基础目录结构和模块初始化" />
    <MESSAGE value="1.2 创建配置文件和参数解析" />
    <MESSAGE value="1.3 创建训练入口空框架" />
    <MESSAGE value="2.1 创建模型工厂函数和基础架构适配" />
    <MESSAGE value="2.2 迁移主要骨干网络" />
    <MESSAGE value="2.3 创建轻量级检测头工具和接口层" />
    <MESSAGE value="2.4 完善训练入口中的模型创建和初始化逻辑" />
    <MESSAGE value="3.1 建立最小训练循环框架" />
    <MESSAGE value="3.2 完善训练循环稳定性和完整性" />
    <MESSAGE value="4.1 核心损失函数实现" />
    <MESSAGE value="4.2 配置系统扩展" />
    <MESSAGE value="4.3 训练循环集成" />
    <MESSAGE value="5.1 基础工具函数迁移（最小可验证单元）" />
    <MESSAGE value="5.2 数据集基础框架（继承TableDataset）" />
    <option name="LAST_COMMIT_MESSAGE" value="5.2 数据集基础框架（继承TableDataset）" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/LORE-TSR/src/main.py</url>
          <line>98</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/LORE-TSR/src/lib/datasets/dataset/table_mid.py</url>
          <line>25</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/LORE-TSR/src/lib/trains/base_trainer.py</url>
          <line>90</line>
          <option name="timeStamp" value="3" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/LORE-TSR/src/lib/trains/base_trainer.py</url>
          <line>18</line>
          <option name="timeStamp" value="4" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/LORE-TSR/src/lib/trains/ctdet.py</url>
          <line>104</line>
          <option name="timeStamp" value="5" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/LORE-TSR/src/main.py</url>
          <line>85</line>
          <option name="timeStamp" value="6" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/TSRTransplantation$analyze_coco_dataset.coverage" NAME="analyze_coco_dataset Coverage Results" MODIFIED="1752729663162" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/this_vibecoding/vibe_utils" />
  </component>
</project>