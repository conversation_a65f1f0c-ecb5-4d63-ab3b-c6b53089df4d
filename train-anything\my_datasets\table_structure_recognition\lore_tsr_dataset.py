#!/usr/bin/env python3
"""
LORE-TSR 数据集类实现

迭代5步骤5.2：数据集基础框架
继承train-anything框架的TableDataset基类，支持WTW分布式标注格式
实现WTW到LORE格式的完整转换逻辑

本模块遵循"重构适配框架入口"策略：
- 继承TableDataset基类，复用数据加载基础设施
- 实现WTW到LORE格式的准确转换
- 深度集成train-anything的配置和训练系统
- 参考Cycle-CenterNet-MS最佳实践
"""

import os
import torch
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
import sys
from pathlib import Path
import logging

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from my_datasets.table_structure_recognition.table_dataset import TableDataset

# 设置日志
logger = logging.getLogger(__name__)


class LoreTsrDataset(TableDataset):
    """
    LORE-TSR数据集类，继承TableDataset基类

    迭代5步骤5.2：数据集基础框架
    实现WTW分布式标注格式到LORE格式的完整转换

    主要功能：
    1. 继承TableDataset的数据加载基础设施
    2. 实现WTW到LORE格式的准确转换
    3. 支持LORE-TSR的目标生成需求
    4. 与train-anything框架深度集成
    """

    def __init__(self, config, mode='train'):
        """
        初始化LORE-TSR数据集

        Args:
            config: OmegaConf配置对象，包含数据和模型配置
            mode: 数据集模式 ('train', 'val', 'test')
        """
        # 从配置中获取数据根目录
        data_root = config.data.dataset.data_root

        # 调用父类初始化，复用TableDataset的数据加载基础设施
        super().__init__(
            data_root=data_root,
            mode=mode,
            target_size=(config.data.processing.image_size[0], config.data.processing.image_size[1]),
            debug=config.data.dataset.get('debug', False),
            max_samples=config.data.dataset.get('max_samples', None)
        )

        # 保存配置和模式
        self.config = config
        self.mode = mode

        # 设置LORE-TSR特定参数
        self.input_h = config.data.processing.image_size[0]
        self.input_w = config.data.processing.image_size[1]
        self.down_ratio = config.data.processing.down_ratio
        self.num_classes = config.model.heads.hm
        self.output_h = self.input_h // self.down_ratio
        self.output_w = self.input_w // self.down_ratio

        # LORE-TSR特定配置
        self.max_objs = config.data.processing.get('max_objs', 500)
        self.gaussian_iou = config.data.processing.get('gaussian_iou', 0.7)

        logger.info(f"LoreTsrDataset初始化完成: mode={mode}, "
                   f"input_size=({self.input_h}, {self.input_w}), "
                   f"output_size=({self.output_h}, {self.output_w}), "
                   f"down_ratio={self.down_ratio}, num_classes={self.num_classes}")

        # 验证配置
        self._validate_config()

    def _validate_config(self):
        """验证配置参数的有效性"""
        assert self.input_h > 0 and self.input_w > 0, "图像尺寸必须为正数"
        assert self.down_ratio > 0, "下采样比例必须为正数"
        assert self.num_classes > 0, "类别数必须为正数"
        assert self.output_h > 0 and self.output_w > 0, "输出尺寸必须为正数"

        logger.debug(f"配置验证通过: input=({self.input_h}, {self.input_w}), "
                    f"output=({self.output_h}, {self.output_w}), "
                    f"down_ratio={self.down_ratio}")

    def _convert_wtw_to_lore_format(self, annotation: Dict) -> Dict:
        """
        将WTW分布式标注格式转换为LORE格式

        WTW格式：
        - bbox: {p1: [x1,y1], p2: [x2,y2], p3: [x3,y3], p4: [x4,y4]}
        - lloc: {start_row: int, end_row: int, start_col: int, end_col: int}

        LORE格式：
        - segmentation: [[x1,y1,x2,y2,x3,y3,x4,y4]] (展平的四点坐标)
        - logic_axis: [start_row, end_row, start_col, end_col]
        - category_id: 1 (固定为前景类)

        Args:
            annotation: WTW格式的标注数据

        Returns:
            Dict: LORE格式的标注数据
        """
        lore_annotations = []

        if 'annotations' not in annotation:
            logger.warning("标注中缺少annotations字段")
            return {'annotations': []}

        for ann in annotation['annotations']:
            try:
                lore_ann = self._convert_single_annotation(ann)
                if lore_ann is not None:
                    lore_annotations.append(lore_ann)
            except Exception as e:
                logger.warning(f"转换单个标注失败: {e}")
                continue

        return {
            'annotations': lore_annotations,
            'image': annotation.get('image', {}),
            'info': annotation.get('info', {})
        }

    def _convert_single_annotation(self, ann: Dict) -> Optional[Dict]:
        """
        转换单个标注对象

        Args:
            ann: 单个WTW格式标注

        Returns:
            Optional[Dict]: LORE格式标注，转换失败时返回None
        """
        # 检查必需字段
        if 'bbox' not in ann or 'lloc' not in ann:
            logger.warning(f"标注缺少必需字段: {ann.keys()}")
            return None

        bbox = ann['bbox']
        lloc = ann['lloc']

        # 验证bbox格式
        required_points = ['p1', 'p2', 'p3', 'p4']
        if not all(point in bbox for point in required_points):
            logger.warning(f"bbox缺少必需点: {bbox.keys()}")
            return None

        # 验证lloc格式
        required_lloc_fields = ['start_row', 'end_row', 'start_col', 'end_col']
        if not all(field in lloc for field in required_lloc_fields):
            logger.warning(f"lloc缺少必需字段: {lloc.keys()}")
            return None

        try:
            # 转换bbox为segmentation（展平的四点坐标）
            segmentation = []
            for point_name in required_points:
                point = bbox[point_name]
                if not isinstance(point, (list, tuple)) or len(point) != 2:
                    logger.warning(f"无效的点坐标: {point_name}={point}")
                    return None
                segmentation.extend([float(point[0]), float(point[1])])

            # 转换lloc为logic_axis
            logic_axis = [
                int(lloc['start_row']),
                int(lloc['end_row']),
                int(lloc['start_col']),
                int(lloc['end_col'])
            ]

            # 构建LORE格式标注
            lore_ann = {
                'segmentation': [segmentation],  # LORE格式要求嵌套列表
                'logic_axis': logic_axis,
                'category_id': 1,  # 固定为前景类
                'id': ann.get('id', 0),
                'area': self._calculate_polygon_area(segmentation),
                'iscrowd': 0
            }

            return lore_ann

        except (ValueError, TypeError) as e:
            logger.warning(f"数值转换失败: {e}")
            return None

    def _calculate_polygon_area(self, segmentation: List[float]) -> float:
        """
        计算多边形面积（使用鞋带公式）

        Args:
            segmentation: 展平的坐标列表 [x1,y1,x2,y2,x3,y3,x4,y4]

        Returns:
            float: 多边形面积
        """
        if len(segmentation) != 8:
            return 0.0

        # 重新组织为点对
        points = [(segmentation[i], segmentation[i+1]) for i in range(0, 8, 2)]

        # 鞋带公式
        area = 0.0
        n = len(points)
        for i in range(n):
            j = (i + 1) % n
            area += points[i][0] * points[j][1]
            area -= points[j][0] * points[i][1]

        return abs(area) / 2.0



    def _prepare_lore_targets(self, annotation: Dict) -> Dict:
        """
        准备LORE-TSR特定的目标格式

        步骤5.2：基础目标准备，为后续完整pipeline做准备

        Args:
            annotation: WTW格式标注

        Returns:
            Dict: LORE-TSR目标格式，包含hm, wh, reg等
        """
        # 首先转换为LORE格式
        lore_annotation = self._convert_wtw_to_lore_format(annotation)

        # 使用现有的目标准备逻辑
        try:
            from my_datasets.table_structure_recognition.lore_tsr_target_preparation import prepare_lore_tsr_targets
            targets = prepare_lore_tsr_targets(lore_annotation, self.config)
            return targets
        except Exception as e:
            # 错误处理：创建默认目标
            logger.warning(f"目标准备失败，使用默认目标: {e}")
            return self._create_default_targets()

    def _create_default_targets(self) -> Dict:
        """
        创建默认目标（当目标准备失败时使用）

        Returns:
            Dict: 默认的LORE-TSR目标格式
        """
        output_h, output_w = self.output_h, self.output_w
        max_objs = self.max_objs

        targets = {
            'hm': torch.zeros(self.num_classes, output_h, output_w, dtype=torch.float32),
            'wh': torch.zeros(max_objs, 8, dtype=torch.float32),
            'reg': torch.zeros(max_objs, 2, dtype=torch.float32),
            'reg_mask': torch.zeros(max_objs, dtype=torch.float32),
            'ind': torch.zeros(max_objs, dtype=torch.long),
            'num_objs': torch.tensor(0, dtype=torch.long)
        }

        logger.debug(f"创建默认目标: hm={targets['hm'].shape}, "
                    f"wh={targets['wh'].shape}, max_objs={max_objs}")

        return targets

    def __getitem__(self, index: int) -> Dict[str, Any]:
        """
        获取单个样本

        Args:
            index: 样本索引

        Returns:
            Dict: 包含图像、标注和目标的样本字典
        """
        try:
            # 调用父类获取基础样本（图像和WTW格式标注）
            sample = super().__getitem__(index)

            # 添加LORE-TSR特定的目标准备
            if 'annotation' in sample:
                lore_targets = self._prepare_lore_targets(sample['annotation'])

                # 确保targets字段存在
                if 'targets' not in sample:
                    sample['targets'] = {}
                sample['targets'].update(lore_targets)

                # 添加调试信息
                if logger.isEnabledFor(logging.DEBUG):
                    self._log_sample_info(sample, index)
            else:
                logger.warning(f"样本{index}缺少annotation字段")
                # 创建默认目标
                sample['targets'] = self._create_default_targets()

            return sample

        except Exception as e:
            logger.error(f"获取样本{index}失败: {e}")
            # 返回默认样本
            return self._create_default_sample()

    def _log_sample_info(self, sample: Dict, index: int):
        """记录样本信息（调试用）"""
        if 'targets' in sample:
            targets = sample['targets']
            logger.debug(f"样本{index}: "
                        f"hm_shape={targets.get('hm', torch.empty(0)).shape}, "
                        f"num_objs={targets.get('num_objs', 0)}")

    def _create_default_sample(self) -> Dict[str, Any]:
        """创建默认样本（当样本加载失败时使用）"""
        return {
            'image': torch.zeros(3, self.input_h, self.input_w, dtype=torch.float32),
            'targets': self._create_default_targets(),
            'annotation': {'annotations': []},
            'image_id': 0
        }

    def __len__(self) -> int:
        """返回数据集大小"""
        return super().__len__()

    def get_sample_info(self, index: int) -> Dict[str, Any]:
        """
        获取样本信息（不加载图像）

        Args:
            index: 样本索引

        Returns:
            Dict: 样本信息
        """
        try:
            # 获取基础信息
            info = super().get_sample_info(index) if hasattr(super(), 'get_sample_info') else {}

            # 添加LORE-TSR特定信息
            info.update({
                'dataset_type': 'LoreTsrDataset',
                'mode': self.mode,
                'input_size': (self.input_h, self.input_w),
                'output_size': (self.output_h, self.output_w),
                'down_ratio': self.down_ratio,
                'num_classes': self.num_classes
            })

            return info

        except Exception as e:
            logger.warning(f"获取样本{index}信息失败: {e}")
            return {'error': str(e)}
